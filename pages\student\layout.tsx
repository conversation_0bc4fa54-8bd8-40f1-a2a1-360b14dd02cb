// pages/student/layout.tsx (Server Component - NO hooks allowed)
import { StudentAuthWrapper } from '../../components/authwrappers/StudentAuthWrapper.client';
import { LayoutWrapper } from '../../components/auth/student/dual-sidebar';

const StudentLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <StudentAuthWrapper>
        {children}
    </StudentAuthWrapper>
  );
};

export default StudentLayout;