'use client';

import { motion, AnimatePresence } from 'motion/react';
import { useEffect, useRef, useState } from 'react';
import { OTPInput } from 'input-otp';
import type { SlotProps} from "input-otp"
import { cn } from '../../lib/utils';
import { toast } from './toast.client';

interface AnimatedNumberProps {
  value: string | null;
  placeholder: string;
}

function Separator() {
  return <div className="h-0.5 w-2 rounded-full bg-black dark:bg-white" />;
}

function AnimatedNumber({ value, placeholder }: AnimatedNumberProps) {
  return (
    <div className="relative flex h-[40px] w-[32px] items-center justify-center overflow-hidden">
      <AnimatePresence initial={false} mode="wait">
        <motion.span
          key={value}
          initial={{ opacity: 0.25, y: 15 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 0 }}
          transition={{ duration: 0.08, ease: 'easeInOut' }}
          className={cn('absolute', value === null ? 'text-primary/10' : '')}
        >
          {value ?? placeholder}
        </motion.span>
      </AnimatePresence>
    </div>
  );
}

function Slot(props: SlotProps & { isShaking?: boolean; isVerifying: boolean; delay: number; }) {
  const placeholderChar = '0';

  return (
    <motion.div
      layout
      className={cn(
        'relative flex h-[40px] w-[36px] items-center justify-center rounded-[10px] font-manrope_1 font-base bg-gradient-to-br from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] ',
        props.isVerifying && 'fast-pulse duration-100 text-black/60 dark:text-white/60',
      )}
      style={{
        animationDelay: `${props.delay}ms`,
      }}
    >
      <AnimatedNumber value={props.char} placeholder={placeholderChar} />
      {props.isActive ? (
        <motion.div
          layoutId="indicator"
          className={cn(
            'absolute inset-0 z-10 rounded-[10px] border-3',
            props.isShaking ? 'border-rose-400' : 'border-blue-400',
            props.isVerifying && 'border-none'
          )}
          transition={{ duration: 0.12, ease: 'easeInOut' }}
        />
      ) : null}
    </motion.div>
  );
}

interface FamilyStyleOTPProps {
  value?: string;
  onChange?: (value: string) => void;
  onSubmit?: (value: string) => Promise<void> | void;
  loading?: boolean;
  error?: string;
  email?: string;
  onResend?: () => void;
  directAccess?: boolean;
}

export default function FamilyStyleOTP({
  value: externalValue,
  onChange: externalOnChange,
  onSubmit,
  loading: externalLoading = false,
  error: externalError = '',
  email,
  onResend,
  directAccess = false
}: FamilyStyleOTPProps) {
  const [internalValue, setInternalValue] = useState('');
  const [disableSubmitButton, setDisableSubmitButton] = useState(true);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isShaking, setIsShaking] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const otpRef = useRef<HTMLInputElement>(null);

  // Use external value if provided, otherwise use internal state
  const value = externalValue !== undefined ? externalValue : internalValue;
  const setValue = externalOnChange || setInternalValue;
  const loading = externalLoading || isVerifying;
  const error = externalError || errorMessage;



  useEffect(() => {
    setDisableSubmitButton(value.length !== 6);
  }, [value]);

  useEffect(() => {
    if (externalError) {
      setIsShaking(true);
    }
  }, [externalError]);

  const handleSubmit = async () => {
    // Fix for an issue where user can briefly click submit again after submitting,
    // prevent submission if one is already in progress.
    if (loading) return;

    if (onSubmit) {
      try {
        await onSubmit(value);
      } catch (err) {
        setIsShaking(true);
        setErrorMessage('Invalid verification code');
      }
    } else {
      // Fallback demo behavior
      setIsVerifying(true);
      setDisableSubmitButton(true);
      setErrorMessage('');

      setTimeout(() => {
        if (value === '123456') {
          // Success - no toast needed as this is just demo behavior
          // Real authentication handles success differently
        } else {
          setIsShaking(true);
          setErrorMessage('Invalid validation code');
        }

        setValue('');
        setIsVerifying(false);

        // Focus back to the first slot after submitting.
        if (otpRef.current) {
          otpRef.current.focus();
          otpRef.current.setSelectionRange(0, 0);
        }
      }, 2000);
    }
  };

  const handleResend = () => {
    if (onResend) {
      onResend();
    } else {
      toast.success('Verification code has been sent', {
        description: 'Normally you would get a code but this is just a prototype ;)',
      });
    }
  };

  return (
    <div className="my-2 flex flex-col items-center">
      <div className="mb-4 text-center">
        <p className="text-black/80 dark:text-white/80 font-manrope_1 text-sm">
          {directAccess
            ? "Enter your valid OTP code below."
            : email
              ? <>We've sent you a verification code to <span className="font-semibold text-black dark:text-white">{email}</span>.</>
              : "We've sent you a verification code to your email."
          }
        </p>

      </div>
      <motion.div
        animate={isShaking ? { x: [0, -5, 5, -2.5, 2.5, 0] } : { x: 0 }}
        transition={{ duration: 0.3 }}
        onAnimationComplete={() => setIsShaking(false)}
      >
        <OTPInput
          ref={otpRef}
          value={value}
          maxLength={6}
          containerClassName="group flex gap-2 items-center justify-center mb-2"
          onChange={(newValue) => {
            // Error if value doesn't contain number
            if (!/^\d*$/.test(newValue)) {
              setIsShaking(true);
              return;
            }
            setValue(newValue);
            if (error) setErrorMessage('');
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              if (value.length < 6) return;
              handleSubmit();
            }
          }}
          render={({ slots }) => (
            <>
              <div className="flex gap-1">
                {slots.slice(0, 3).map((slot, idx) => (
                  <Slot
                    key={idx}
                    {...slot}
                    isShaking={isShaking}
                    isVerifying={loading}
                    delay={idx * 100}
                  />
                ))}
              </div>
              <Separator />
              <div className="flex gap-1">
                {slots.slice(3).map((slot, idx) => (
                  <Slot
                    key={idx}
                    {...slot}
                    isShaking={isShaking}
                    isVerifying={loading}
                    delay={(idx + 3) * 100}
                  />
                ))}
              </div>
            </>
          )}
        />
      </motion.div>

      {!directAccess && onResend && (
        <span className="font-manrope_1 text-black/80 dark:text-white/80 mb-2 items-center text-xs text-center">
          Didn{"'"}t receive a code?{' '}
          <button
            className="cursor-pointer font-manrope_1 text-sm font-semibold text-blue-500"
            onClick={handleResend}
          >
            Resend
          </button>
        </span>
      )}
      {directAccess && (
        <div className="mb-0 h-6"></div>
      )}
      <div className="mb-0 h-[28px] flex justify-center">
        <AnimatePresence mode="wait" initial={false}>
          {error && (
            <motion.p
              key="error-message"
              className="flex h-[28px] w-fit items-center rounded-full bg-rose-50 px-2.5 text-xs font-manrope_1 font-medium text-rose-500"
              initial={{ scale: 0.2, opacity: 0, x: -80 }}
              animate={{ scale: 1, opacity: 1, x: 0 }}
              exit={{ scale: 0.2, opacity: 0, x: -80 }}
              transition={{ duration: 0.1 }}
            >
              {error}
            </motion.p>
          )}
        </AnimatePresence>
      </div>
      <button
        disabled={disableSubmitButton}
        onClick={() => {
          handleSubmit();
        }}
        className={cn(
          'flex h-[40px] w-full cursor-pointer disabled:opacity-40 items-center justify-center rounded-full select-none disabled:cursor-not-allowed bg-gradient-to-br from-zinc-100 to-zinc-200 text-zinc-800  shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] ',
          'transform transition-transform duration-200 ease-out hover:scale-95 active:scale-95 disabled:scale-100',
          loading
            ? 'text-zinc-800 dark:text-zinc-50  disabled:opacity-60 dark:disabled:opacity-60 '
            : 'bg-gradient-to-br from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e]  dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]  ',
        )}
      >
        <AnimatePresence initial={false}>
          {loading ? (
            <motion.div
              className="flex w-fit items-center gap-1"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="animate-spin"
              >
                <path
                  d="M14 8C14 8.78793 13.8448 9.56815 13.5433 10.2961C13.2417 11.0241 12.7998 11.6855 12.2426 12.2426C11.6855 12.7998 11.024 13.2418 10.2961 13.5433C9.56814 13.8448 8.78793 14 8 14C7.21206 14 6.43185 13.8448 5.70389 13.5433C4.97594 13.2418 4.31451 12.7998 3.75736 12.2426C3.2002 11.6855 2.75825 11.0241 2.45672 10.2961C2.15519 9.56815 2 8.78793 2 8C2 7.21207 2.15519 6.43186 2.45672 5.7039C2.75825 4.97595 3.2002 4.31451 3.75736 3.75736C4.31451 3.20021 4.97594 2.75825 5.7039 2.45673C6.43185 2.1552 7.21207 2 8 2C8.78793 2 9.56814 2.1552 10.2961 2.45673C11.0241 2.75826 11.6855 3.20021 12.2426 3.75736C12.7998 4.31452 13.2417 4.97595 13.5433 5.7039C13.8448 6.43186 14 7.21207 14 8L14 8Z"
                  stroke="#DADADA"
                  strokeWidth="3"
                />
                <path
                  d="M14 8C14 8.94687 13.7759 9.88029 13.346 10.7239C12.9162 11.5676 12.2927 12.2976 11.5267 12.8541C10.7607 13.4107 9.87381 13.778 8.9386 13.9261C8.0034 14.0743 7.04641 13.9989 6.14589 13.7063"
                  stroke="#191919"
                  strokeOpacity="0.36"
                  strokeWidth="3"
                  strokeLinecap="round"
                />
              </svg>
              Verifying
            </motion.div>
          ) : (
            <span>
              Submit
            </span>
          )}
        </AnimatePresence>
      </button>
    </div>
  );
}