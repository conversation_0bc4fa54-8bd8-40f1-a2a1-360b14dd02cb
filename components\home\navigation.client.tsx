import { useState, useRef, useEffect } from 'react';
import { Link, Image } from 'blade/client/components';
import { useUnifiedSession } from '../../lib/auth-client';
import type { AuthType } from '../../lib/auth';
import { Tabs } from '@base-ui-components/react/tabs';
import { useQueryState } from 'blade/client/hooks';
import { useLocation } from 'blade/hooks';
import { motion, AnimatePresence } from 'motion/react';
import { Menu, X } from 'lucide-react';
import { Badge } from '../ui/badge.client';

export function Navigation() {
  const location = useLocation();
  const [userType, setUserType] = useQueryState('type');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const navRef = useRef<HTMLDivElement>(null);

  // Check if we're on the home page
  const isHomePage = location.pathname === '/';

  // Determine which tab should be active based on current page
  const getActiveTab = () => {
    if (isHomePage) {
      return userType || 'student';
    }

    // Map pathname to tab value
    if (location.pathname === '/login') return 'login';
    if (location.pathname === '/pricing') return 'pricing';
    if (location.pathname === '/docs') return 'docs';

    // Default fallback (shouldn't normally happen)
    return 'student';
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (navRef.current && !navRef.current.contains(event.target as Node)) {
        setIsMobileMenuOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <header className="p-2 md:p-4 sticky top-0 z-50 w-full">
      <div className="relative w-full max-w-lg mx-auto" ref={navRef}>
        <div className="flex items-center w-full backdrop-blur-sm rounded-full p-1 text-sm h-10 select-none">
          
          {/* Mobile Layout */}
          <div className="md:hidden flex items-center justify-between w-full">
            {/* Left: Logo */}
            <div className="flex items-center pl-2">
              <Link href="/" className="font-manrope_1 text-zinc-900 dark:text-white font-semibold">
                <a className="flex items-center">
                  <Image
                    src="/logo-lightmode.png"
                    alt="Penned Logo"
                    className="w-6 h-6 dark:hidden"
                    width={32}
                    height={32}
                  />
                  <Image
                    src="/logo-darkmode.png"
                    alt="Penned Logo"
                    className="w-6 h-6 hidden dark:block"
                    width={32}
                    height={32}
                  />
                </a>
              </Link>
            </div>

            {/* Center: Tabs */}
            <div className="flex-1 flex justify-center">
              <Tabs.Root className="relative flex items-center" value={getActiveTab()} onValueChange={(value) => {
                if (isHomePage && ['student', 'teacher', 'school_admin'].includes(value)) {
                  setUserType(value);
                }
              }}>
                <Tabs.List className="relative z-0 flex gap-1 items-center">
                  {isHomePage ? (
                    <>
                      <Tabs.Tab
                        className="flex h-8 font-manrope_1 rounded-full items-center justify-center border-0 px-2 text-xs font-medium text-black/60 dark:text-white/60 outline-none select-none before:inset-x-0 before:inset-y-1 before:rounded-sm before:-outline-offset-1 before:outline-blue-800 focus-visible:relative focus-visible:before:absolute focus-visible:before:outline-2 data-[selected]:text-black/90 dark:data-[selected]:text-white/80 data-[selected]:font-medium"
                        value="student"
                      >
                        <span>Student</span>
                      </Tabs.Tab>
                      <Tabs.Tab
                        className="flex h-8 font-manrope_1 rounded-full items-center justify-center border-0 px-2 text-xs font-medium text-black/60 dark:text-white/60 outline-none select-none before:inset-x-0 before:inset-y-1 before:rounded-sm before:-outline-offset-1 before:outline-blue-800 focus-visible:relative focus-visible:before:absolute focus-visible:before:outline-2 data-[selected]:text-black/90 dark:data-[selected]:text-white/80 data-[selected]:font-medium"
                        value="teacher"
                      >
                        <span>Teacher</span>
                      </Tabs.Tab>
                      <Tabs.Tab
                        className="flex h-8 font-manrope_1 rounded-full items-center justify-center border-0 px-2 text-xs font-medium text-black/60 dark:text-white/60 outline-none select-none before:inset-x-0 before:inset-y-1 before:rounded-sm before:-outline-offset-1 before:outline-blue-800 focus-visible:relative focus-visible:before:absolute focus-visible:before:outline-2 data-[selected]:text-black/90 dark:data-[selected]:text-white/80 data-[selected]:font-medium"
                        value="school_admin"
                      >
                        <span>School</span>
                      </Tabs.Tab>
                    </>
                  ) : (
                    <Tabs.Tab
                      className="flex h-8 font-manrope_1 rounded-full items-center justify-center border-0 px-2 text-xs font-medium text-black/60 dark:text-white/60 outline-none select-none before:inset-x-0 before:inset-y-1 before:rounded-sm before:-outline-offset-1 before:outline-blue-800 focus-visible:relative focus-visible:before:absolute focus-visible:before:outline-2 data-[selected]:text-black/90 dark:data-[selected]:text-white/80 data-[selected]:font-medium"
                      value="home"
                    >
                      <Link href="/">
                        <span>Home</span>
                      </Link>
                    </Tabs.Tab>
                  )}
                  <Tabs.Indicator className="absolute top-1/2 left-0 z-[-1] h-8 w-[var(--active-tab-width)] -translate-y-1/2 translate-x-[var(--active-tab-left)] rounded-full bg-gradient-to-b from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] transition-all duration-100 ease-in-out" />
                </Tabs.List>
              </Tabs.Root>
            </div>

            {/* Right: Menu Button */}
            <div className="pr-2">
              <button onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)} className="text-zinc-900 dark:text-white text-xl p-1">
                <AnimatePresence mode="wait">
                  {isMobileMenuOpen ? (
                    <motion.div key="x" initial={{ rotate: -90, scale: 0 }} animate={{ rotate: 0, scale: 1 }} exit={{ rotate: 90, scale: 0 }} transition={{ duration: 0.2 }}>
                      <X className="w-5 h-5" />
                    </motion.div>
                  ) : (
                    <motion.div key="menu" initial={{ rotate: 90, scale: 0 }} animate={{ rotate: 0, scale: 1 }} exit={{ rotate: -90, scale: 0 }} transition={{ duration: 0.2 }}>
                      <Menu className="w-5 h-5" />
                    </motion.div>
                  )}
                </AnimatePresence>
              </button>
            </div>
          </div>

          {/* Desktop Layout */}
          <div className="hidden md:flex items-center justify-center w-full">
            <div className="flex items-center">
              {/* Logo section - separate from tabs */}
              <Link href="/" className="pl-2 font-manrope_1 text-zinc-900 dark:text-white font-semibold">
                <a className="flex items-start gap-1 relative">
                  {/* Light mode logo - shows in light mode */}
                  <Image
                    src="/logo-lightmode.png"
                    alt="Penned Logo"
                    className="w-6 h-6 dark:hidden"
                    width={32}
                    height={32}
                  />
                  {/* Dark mode logo - shows in dark mode */}
                  <Image
                    src="/logo-darkmode.png"
                    alt="Penned Logo"
                    className="w-6 h-6 hidden dark:block"
                    width={32}
                    height={32}
                  />
                
                </a>
              </Link>
              
              <div className="border-l border-zinc-300 dark:border-zinc-600 h-4 mx-4" />
              
              {/* Tabs section */}
              <Tabs.Root className="relative flex items-center" value={getActiveTab()} onValueChange={(value) => {
                if (isHomePage && ['student', 'teacher', 'school_admin'].includes(value)) {
                  setUserType(value);
                }
              }}>
                <Tabs.List className="relative z-0 flex gap-1 items-center">
                  {/* User type tabs or Home button */}
                  {isHomePage ? (
                    <>
                      <Tabs.Tab
                        className="flex h-8 font-manrope_1 rounded-full items-center justify-center border-0 px-2 text-xs font-medium text-black/60 dark:text-white/60 outline-none select-none before:inset-x-0 before:inset-y-1 before:rounded-sm before:-outline-offset-1 before:outline-blue-800 focus-visible:relative focus-visible:before:absolute focus-visible:before:outline-2 data-[selected]:text-black/90 dark:data-[selected]:text-white/80 data-[selected]:font-medium"
                        value="student"
                      >
                        <span>Student</span>
                      </Tabs.Tab>
                      <Tabs.Tab
                        className="flex h-8 font-manrope_1 rounded-full items-center justify-center border-0 px-2 text-xs font-medium text-black/60 dark:text-white/60 outline-none select-none before:inset-x-0 before:inset-y-1 before:rounded-sm before:-outline-offset-1 before:outline-blue-800 focus-visible:relative focus-visible:before:absolute focus-visible:before:outline-2 data-[selected]:text-black/90 dark:data-[selected]:text-white/80 data-[selected]:font-medium"
                        value="teacher"
                      >
                        <span>Teacher</span>
                      </Tabs.Tab>
                      <Tabs.Tab
                        className="flex h-8 font-manrope_1 rounded-full items-center justify-center border-0 px-2 text-xs font-medium text-black/60 dark:text-white/60 outline-none select-none before:inset-x-0 before:inset-y-1 before:rounded-sm before:-outline-offset-1 before:outline-blue-800 focus-visible:relative focus-visible:before:absolute focus-visible:before:outline-2 data-[selected]:text-black/90 dark:data-[selected]:text-white/80 data-[selected]:font-medium"
                        value="school_admin"
                      >
                        <span>School</span>
                      </Tabs.Tab>
                    </>
                  ) : (
                    <Tabs.Tab
                      className="flex h-8 font-manrope_1 rounded-full items-center justify-center border-0 px-2 text-xs font-medium text-black/60 dark:text-white/60 outline-none select-none before:inset-x-0 before:inset-y-1 before:rounded-sm before:-outline-offset-1 before:outline-blue-800 focus-visible:relative focus-visible:before:absolute focus-visible:before:outline-2 data-[selected]:text-black/90 dark:data-[selected]:text-white/80 data-[selected]:font-medium"
                      value="home"
                    >
                      <Link href="/">
                        <span>Home</span>
                      </Link>
                    </Tabs.Tab>
                  )}

                  {/* Always visible tabs */}
                  <Tabs.Tab
                    className="flex h-8 font-manrope_1 rounded-full items-center justify-center border-0 px-2 text-xs font-medium text-black/60 dark:text-white/60 outline-none select-none before:inset-x-0 before:inset-y-1 before:rounded-sm before:-outline-offset-1 before:outline-blue-800 focus-visible:relative focus-visible:before:absolute focus-visible:before:outline-2 data-[selected]:text-black/90 dark:data-[selected]:text-white/80 data-[selected]:font-medium cursor-not-allowed opacity-50"
                    value="pricing"
                    disabled
                    title="Coming soon - /pricing"
                  >
                    <span className="mr-1">Pricing</span>
                    <Badge
                      variant="outline"
                      className="text-[8px] px-1 py-0 h-4 dark:hidden pointer-events-none"
                    >
                      soon
                    </Badge>
                    <Badge
                      className="text-[8px] px-1 py-0 h-4 hidden dark:block pointer-events-none"
                    >
                      soon
                    </Badge>
                  </Tabs.Tab>

                  <Tabs.Tab
                    className="flex h-8 font-manrope_1 rounded-full items-center justify-center border-0 px-2 text-xs font-medium text-black/60 dark:text-white/60 outline-none select-none before:inset-x-0 before:inset-y-1 before:rounded-sm before:-outline-offset-1 before:outline-blue-800 focus-visible:relative focus-visible:before:absolute focus-visible:before:outline-2 data-[selected]:text-black/90 dark:data-[selected]:text-white/80 data-[selected]:font-medium cursor-not-allowed opacity-50"
                    value="docs"
                    disabled
                    title="Coming soon - /docs"
                  >
                    <span className="mr-1">Docs</span>
                    <Badge
                      variant="outline"
                      className="text-[8px] px-1 py-0 h-4 dark:hidden pointer-events-none"
                    >
                      soon
                    </Badge>
                    <Badge
                      className="text-[8px] px-1 py-0 h-4 hidden dark:block pointer-events-none"
                    >
                      soon
                    </Badge>
                  </Tabs.Tab>

                  <Tabs.Tab
                    className="flex h-8 font-manrope_1 rounded-full items-center justify-center border-0 px-2 text-xs font-medium text-black/60 dark:text-white/60 outline-none select-none before:inset-x-0 before:inset-y-1 before:rounded-sm before:-outline-offset-1 before:outline-blue-800 focus-visible:relative focus-visible:before:absolute focus-visible:before:outline-2 data-[selected]:text-black/90 dark:data-[selected]:text-white/80 data-[selected]:font-medium"
                    value="login"
                  >
                    <Link href="/login">
                      <span>Login</span>
                    </Link>
                  </Tabs.Tab>

                  <Tabs.Indicator className="absolute top-1/2 left-0 z-[-1] h-8 w-[var(--active-tab-width)] -translate-y-1/2 translate-x-[var(--active-tab-left)] rounded-full bg-gradient-to-b from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] transition-all duration-100 ease-in-out" />
                </Tabs.List>
              </Tabs.Root>
            </div>
          </div>
        </div>

        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, y: -20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{ duration: 0.2, ease: "easeOut" }}
              className="md:hidden absolute top-12 left-0 w-full rounded-2xl backdrop-blur-sm bg-gradient-to-b from-zinc-100 to-zinc-200 dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] p-4 z-20"
            >
              <div className="flex flex-col gap-3">
                {/* Coming Soon Section */}
                <div className="text-center">
                  <h3 className="text-sm font-semibold text-zinc-800 dark:text-zinc-200 font-manrope_1 mb-3">
                    Coming Soon
                  </h3>
                  <div className="flex flex-col gap-2">
                    {/* Pricing */}
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 }}
                      className="flex items-center justify-between p-3 rounded-xl bg-white/50 dark:bg-black/20 border border-zinc-200/50 dark:border-zinc-700/50"
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-zinc-700 dark:text-zinc-300 font-manrope_1">
                          Pricing
                        </span>
                        <Badge
                          variant="outline"
                          className="text-[8px] px-1.5 py-0.5 h-4 dark:hidden"
                        >
                          soon
                        </Badge>
                        <Badge
                          className="text-[8px] px-1.5 py-0.5 h-4 hidden dark:block"
                        >
                          soon
                        </Badge>
                      </div>
                      <span className="text-xs text-zinc-500 dark:text-zinc-400 font-mono">
                        /pricing
                      </span>
                    </motion.div>

                    {/* Docs */}
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2 }}
                      className="flex items-center justify-between p-3 rounded-xl bg-white/50 dark:bg-black/20 border border-zinc-200/50 dark:border-zinc-700/50"
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-zinc-700 dark:text-zinc-300 font-manrope_1">
                          Documentation
                        </span>
                        <Badge
                          variant="outline"
                          className="text-[8px] px-1.5 py-0.5 h-4 dark:hidden"
                        >
                          soon
                        </Badge>
                        <Badge
                          className="text-[8px] px-1.5 py-0.5 h-4 hidden dark:block"
                        >
                          soon
                        </Badge>
                      </div>
                      <span className="text-xs text-zinc-500 dark:text-zinc-400 font-mono">
                        /docs
                      </span>
                    </motion.div>

                    {/* Login */}
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 }}
                      className="flex items-center justify-between p-3 rounded-xl bg-white/50 dark:bg-black/20 border border-zinc-200/50 dark:border-zinc-700/50"
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-zinc-700 dark:text-zinc-300 font-manrope_1">
                          Login
                        </span>
                        <Badge
                          variant="outline"
                          className="text-[8px] px-1.5 py-0.5 h-4 dark:hidden"
                        >
                          soon
                        </Badge>
                        <Badge
                          className="text-[8px] px-1.5 py-0.5 h-4 hidden dark:block"
                        >
                          soon
                        </Badge>
                      </div>
                      <span className="text-xs text-zinc-500 dark:text-zinc-400 font-mono">
                        /login
                      </span>
                    </motion.div>
                  </div>
                </div>

                {/* Divider */}
                <div className="border-t border-zinc-300/50 dark:border-zinc-600/50 my-2" />

                {/* Quick Info */}
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="text-center"
                >
                  <p className="text-xs text-zinc-600 dark:text-zinc-400 font-manrope_1">
                    Switch between <strong>Student</strong>, <strong>Teacher</strong>, and <strong>School</strong> views above
                  </p>
                </motion.div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </header>
  );
}
