'use client';
import { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { cn } from '../../../../lib/utils';
import { useIsMobile } from '../../../../hooks/use-mobile';
import { useSharedSidebarState, useSharedRightFlyoutState } from '../../../../stores/sidebar-store.client';
import { SquircleProvider } from '../../../providers/squircle-provider.client';
import {
  SidebarProvider,
  SidebarInset,
  SidebarTrigger,
} from '../../../ui/sidebar.client';
import { AppSidebar } from './app-sidebar.client';
import { Header } from './header.client';
import { RightSidebar } from '../../shared/dual-sidebar/right-sidebar';
import { UserNav } from '../../shared/dual-sidebar/user-nav.client';

interface EnhancedSidebarProps {
  children: React.ReactNode;
  className?: string;
  showUserNav?: boolean;
  showHeader?: boolean;
}

export function EnhancedSidebar({ 
  children, 
  className,
  showUserNav = true,
  showHeader = true
}: EnhancedSidebarProps) {
  const isMobile = useIsMobile();
  const {
    isLeftSidebarOpen,
    isRightSidebarOpen,
    activeFlyout,
    isRightFlyoutOpen,
    rightSidebarContent,
    updateActiveFlyout,
    updateRightSidebarContent,
    toggleRightSidebar,
  } = useSharedSidebarState();

  // Flyout state to hide UserNav when flyout overlay is active (mobile only)
  const { isOpen: flyoutOpen } = useSharedRightFlyoutState();

  // Calculate main content styles based on sidebar states - FASTER TRANSITIONS
  const mainContentStyles = useMemo(() => {
    const styles: Record<string, string> = {
      "transition-all": "true",
      "duration-100": "true", // FASTER: 100ms instead of 300ms
      "ease-out": "true", // FASTER: ease-out instead of ease-in-out
      "flex-1": "true",
      "flex": "true",
      "flex-col": "true",
      "h-full": "true",
      "overflow-hidden": "true",
    };

    // Left sidebar adjustments - FIXED: Only check activeFlyout since we're always in icon mode
    if (!isMobile) {
      if (activeFlyout) {
        styles["ml-[354px]"] = "true"; // Icon sidebar (72px) + flyout (284px) = 356px
      } else {
        styles["ml-[4.5rem]"] = "true"; // Icon sidebar only (72px)
      }
    }

    // Right sidebar adjustments - ONLY for desktop, mobile uses Sheet overlay
    if (!isMobile && isRightSidebarOpen) {
      styles["mr-[350px]"] = "true"; // Desktop: push content left
    }
    // Mobile: No margin adjustments - right sidebar is a Sheet overlay
    // No explicit mr-0 needed as it's the default

  
    return cn(styles);
  }, [isMobile, activeFlyout, isLeftSidebarOpen, isRightSidebarOpen, isRightFlyoutOpen]);

  // Header styles calculation - FASTER TRANSITIONS
  const headerStyles = useMemo(() => {
    const styles: Record<string, string> = {
      "flex": "true",
      "h-16": "true",
      "items-center": "true",
      "mt-0": "true",
      "justify-between": "true",
      "gap-2": "true",
      "px-4": "true",
      "backdrop-blur-md": "true",
      "z-10": "true",
      "transition-all": "true",
      "duration-100": "true", // FASTER: 100ms instead of 200ms
      "ease-out": "true",
    };

    // Left sidebar adjustments - FIXED: Only check activeFlyout since we're always in icon mode
    if (!isMobile) {
      if (activeFlyout) {
        styles["ml-[354px]"] = "true"; // Icon sidebar (72px) + flyout (284px) = 356px
      } else {
        styles["ml-[4rem]"] = "true"; // Icon sidebar only (72px)
      }
    } else {
      styles["ml-[4.5rem]"] = "true";
    }

    // Right sidebar adjustments - ONLY for sidebar, NOT flyouts
    if (!isMobile && isRightSidebarOpen) {
      styles["mr-[350px]"] = "true"; // Just sidebar - flyouts are overlays
    }
    // No explicit mr-0 needed as it's the default

    return cn(styles);
  }, [isMobile, activeFlyout, isLeftSidebarOpen, isRightSidebarOpen, isRightFlyoutOpen]);

  return (
    <SquircleProvider>
      
        <SidebarProvider
          defaultOpenLeft={true}
          defaultOpenRight={false}
          className="min-h-screen bg-fixed"
        >
        <div className="flex h-screen w-full overflow-hidden">
          {/* Left App Sidebar - Icon mode with flyouts */}
          <AppSidebar
            variant="floating"
            collapsible="icon"
            flyout={activeFlyout}
            setFlyout={updateActiveFlyout}
          />

          {/* User Navigation - Fixed position top-right */}
          {showUserNav && !(isMobile && flyoutOpen) && (
            <UserNav />
          )}

          {/* Main Content Area */}
          <div className={cn("flex-1 flex flex-col overflow-hidden", mainContentStyles)}>
            {/* Header */}
            {showHeader && (
              <header className={headerStyles}>
                <div className="flex items-center gap-1">
                  <SidebarTrigger className="" />
                  {activeFlyout && (
                    <span className="text-sm text-muted-foreground">
                      • {activeFlyout}
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-white/70">
                    Enhanced Dual Sidebar
                  </span>
                </div>
              </header>
            )}

            {/* Main Content */}
            <SidebarInset className="flex-1 overflow-hidden  rounded-b-xl">
              <main className="flex-1 flex flex-col h-full overflow-hidden">
                <div className="flex-1 overflow-y-auto p-4 custom-scrollbar">
                  <div className="max-w-full mx-auto">
                    {children}
                  </div>
                </div>
              </main>
            </SidebarInset>
          </div>

          {/* Right Sidebar */}
          {rightSidebarContent && (
            <RightSidebar
              content={rightSidebarContent}
            />
          )}
        </div>
        </SidebarProvider>
      
    </SquircleProvider>
  );
}
